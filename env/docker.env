# Docker Environment Configuration
# This file contains environment variables for Docker deployment

# Application Configuration
APP_ENV=docker
APP_NAME=xbit-agent
APP_VERSION=1.0.0

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# Database Configuration (PostgreSQL) - Legacy variables for backward compatibility
# These match the docker-compose.yml service names
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASS=postgres
POSTGRES_DB=agent

# New Database Configuration for Agency (PostgreSQL) - Primary configuration for docker
POSTGRES_AGENCY_HOST=postgres
POSTGRES_AGENCY_PORT=5432
POSTGRES_AGENCY_USER=postgres
POSTGRES_AGENCY_PASS=postgres
POSTGRES_AGENCY_SSL_MODE=disable

# Database URL for migrations (using agency configuration)
# Note: This should match POSTGRES_AGENCY_SSL_MODE setting
DATABASE_URL=********************************************/agent?sslmode=disable

# Redis Configuration (Optional)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASS=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=docker-secret-key-change-in-production
JWT_EXPIRES_TIME=7d
JWT_BUFFER_TIME=1d
JWT_ISSUER=xbit-agent-docker

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=console
LOG_DIRECTOR=log
LOG_SHOW_LINE=true
LOG_IN_CONSOLE=true

# CORS Configuration
CORS_ALLOW_ORIGINS=*
CORS_ALLOW_METHODS=GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS
CORS_ALLOW_HEADERS=Origin,Content-Length,Content-Type,Authorization,X-Requested-With
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_COUNT=15000
RATE_LIMIT_TIME=3600

# Docker Settings
DEBUG=false
ENABLE_PLAYGROUND=true

# NATS Configuration (temporarily disabled - only using nats-meme for now)
# These match the docker-compose.yml service names
# NATS_URL=nats://nats:4222
# NATS_TOKEN=
# NATS_USE_TLS=false
# NATS_USER=
# NATS_PASS=

# NATS Meme Configuration
MEME_NATS_URL=
MEME_NATS_USER=
MEME_NATS_PASS=
MEME_NATS_USE_TLS=false
MEME_NATS_TOKEN=

# NATS Dex Configuration (temporarily disabled - not yet implemented for affiliate events)
# DEX_NATS_URL=
# DEX_NATS_AUTH_TOKEN=
# DEX_NATS_USE_TLS=false
# DEX_NATS_USER=
# DEX_NATS_PASS=

# Docker Compose Settings
COMPOSE_PROJECT_NAME=xbit-agent
