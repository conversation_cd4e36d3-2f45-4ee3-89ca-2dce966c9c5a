# Environment Configuration Guide

This directory contains environment configuration files for different deployment environments.

## Database Configuration

### Agency Database Variables

The application uses the following database configuration variables:

- `POSTGRES_AGENCY_HOST`: Database host for agency service
- `POSTGRES_AGENCY_PORT`: Database port (default: "5432")
- `POSTGRES_AGENCY_USER`: Database user (recommended: "dex")
- `POSTGRES_AGENCY_PASS`: Database password
- `POSTGRES_AGENCY_SSL_MODE`: SSL mode for database connection
- `POSTGRES_DB`: Database name (default: "agent")

### SSL Mode Configuration

The `POSTGRES_AGENCY_SSL_MODE` variable supports the following values:
- `disable`: No SSL (for local development)
- `enable`: Enable SSL connection
- `require`: Require SSL connection (recommended for production)
- `verify-ca`: Require SSL and verify certificate authority
- `verify-full`: Require SSL and verify certificate authority and hostname

## Environment Files

### local.env
- For local development
- Uses `sslmode=disable` for simplicity
- Default PostgreSQL credentials

### docker.env
- For Docker Compose deployment
- Uses service names as hostnames
- Uses `sslmode=disable` for container networking

### unstable.env
- For unstable/testing environment
- Uses actual database credentials
- Currently uses `sslmode=disable`

### staging.env
- For staging environment
- Uses `sslmode=enable` for security
- Placeholder credentials (update before use)

### production.env
- For production environment
- Uses `sslmode=enable` for security
- Placeholder credentials (update before use)



## Configuration Setup

### Required Variables

For any deployment, you need to set these variables:

```bash
POSTGRES_AGENCY_HOST=your-db-host
POSTGRES_AGENCY_PORT=5432
POSTGRES_AGENCY_USER=dex
POSTGRES_AGENCY_PASS=your-secure-password
POSTGRES_AGENCY_SSL_MODE=enable  # or disable for local dev
POSTGRES_DB=agent
```

## Security Recommendations

1. **Always use SSL in production**: Set `POSTGRES_AGENCY_SSL_MODE=enable` or higher
2. **Use strong passwords**: Generate secure passwords for database access
3. **Limit database user permissions**: Use dedicated database users with minimal required permissions
4. **Keep credentials secure**: Never commit real credentials to version control

## Testing Configuration

To test your database configuration:

1. Set your environment variables
2. Run the application
3. Check the logs for successful database connection:
   ```
   Successfully connected to database: agent
   ```

If you see connection errors, verify:
- Database host and port are correct
- Credentials are valid
- SSL mode is compatible with your database setup
- Network connectivity allows the connection
