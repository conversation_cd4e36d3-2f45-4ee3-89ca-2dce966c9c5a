# Environment Configuration Guide

This directory contains environment configuration files for different deployment environments.

## Database Configuration Updates

### New Agency Database Variables

The application now supports dedicated agency database configuration variables that take precedence over the legacy variables:

- `POSTGRES_AGENCY_HOST`: Database host for agency service
- `POSTGRES_AGENCY_PORT`: Database port (default: "5432")
- `POSTGRES_AGENCY_USER`: Database user (recommended: "dex")
- `POSTGRES_AGENCY_PASS`: Database password
- `POSTGRES_AGENCY_SSL_MODE`: SSL mode for database connection

### SSL Mode Configuration

The `POSTGRES_AGENCY_SSL_MODE` variable supports the following values:
- `disable`: No SSL (for local development)
- `require`: Require SSL connection (recommended for production)
- `verify-ca`: Require SSL and verify certificate authority
- `verify-full`: Require SSL and verify certificate authority and hostname

### Fallback Behavior

The configuration uses a fallback mechanism:
1. First, it tries to use `POSTGRES_AGENCY_*` variables
2. If not found, it falls back to legacy `POSTGRES_*` variables
3. Finally, it uses default values

Example configuration priority for host:
```yaml
path: {{ index . "POSTGRES_AGENCY_HOST" | default (index . "POSTGRES_HOST") | default "127.0.0.1" }}
```

## Environment Files

### local.env
- For local development
- Uses `sslmode=disable` for simplicity
- Default PostgreSQL credentials

### docker.env
- For Docker Compose deployment
- Uses service names as hostnames
- Uses `sslmode=disable` for container networking

### unstable.env
- For unstable/testing environment
- Uses actual database credentials
- Currently uses `sslmode=disable`

### staging.env
- For staging environment
- Uses `sslmode=require` for security
- Placeholder credentials (update before use)

### production.env
- For production environment
- Uses `sslmode=require` for security
- Placeholder credentials (update before use)



## Migration Guide

### For Existing Deployments

1. **Backward Compatibility**: Existing deployments will continue to work with legacy `POSTGRES_*` variables.

2. **Gradual Migration**: You can migrate to new variables gradually:
   ```bash
   # Add new variables alongside existing ones
   POSTGRES_HOST=old-host
   POSTGRES_AGENCY_HOST=new-agency-host
   ```

3. **SSL Mode Update**: Add the SSL mode variable to enable secure connections:
   ```bash
   POSTGRES_AGENCY_SSL_MODE=require
   ```

### For New Deployments

Use the new agency variables directly:
```bash
POSTGRES_AGENCY_HOST=your-db-host
POSTGRES_AGENCY_PORT=5432
POSTGRES_AGENCY_USER=dex
POSTGRES_AGENCY_PASS=your-secure-password
POSTGRES_AGENCY_SSL_MODE=require
```

## Security Recommendations

1. **Always use SSL in production**: Set `POSTGRES_AGENCY_SSL_MODE=require` or higher
2. **Use strong passwords**: Generate secure passwords for database access
3. **Limit database user permissions**: Use dedicated database users with minimal required permissions
4. **Keep credentials secure**: Never commit real credentials to version control

## Testing Configuration

To test your database configuration:

1. Set your environment variables
2. Run the application
3. Check the logs for successful database connection:
   ```
   Successfully connected to database: agent
   ```

If you see connection errors, verify:
- Database host and port are correct
- Credentials are valid
- SSL mode is compatible with your database setup
- Network connectivity allows the connection
