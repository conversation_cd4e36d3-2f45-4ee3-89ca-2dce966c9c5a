# Deployment Environment Configuration
# This file contains environment variables for deployment with new database configuration

# Application Configuration
APP_ENV=deployment
APP_NAME=xbit-agent
APP_VERSION=1.0.0

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# Database Configuration (PostgreSQL) - Legacy variables for backward compatibility
POSTGRES_HOST=your-deployment-db-host
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASS=your-deployment-db-password
POSTGRES_DB=agent

# New Database Configuration for Agency (PostgreSQL) - Primary configuration for deployment
POSTGRES_AGENCY_HOST=your-agency-db-host
POSTGRES_AGENCY_PORT=5432
POSTGRES_AGENCY_USER=dex
POSTGRES_AGENCY_PASS=your-agency-db-password
POSTGRES_AGENCY_SSL_MODE=require

# Database URL for migrations (using agency configuration)
DATABASE_URL=*****************************************************************/agent?sslmode=require

# Redis Configuration (Optional)
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASS=your-redis-password
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-deployment-jwt-secret-key
JWT_EXPIRES_TIME=7d
JWT_BUFFER_TIME=1d
JWT_ISSUER=xbit-agent-deployment

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_DIRECTOR=log
LOG_SHOW_LINE=false
LOG_IN_CONSOLE=false

# CORS Configuration
CORS_ALLOW_ORIGINS=https://your-domain.com
CORS_ALLOW_METHODS=GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS
CORS_ALLOW_HEADERS=Origin,Content-Length,Content-Type,Authorization,X-Requested-With
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_COUNT=15000
RATE_LIMIT_TIME=3600

# Production Settings
DEBUG=false
ENABLE_PLAYGROUND=false

# NATS Meme Configuration
MEME_NATS_URL=nats://your-nats-host:4222
MEME_NATS_USER=dex
MEME_NATS_PASS=your-nats-password
MEME_NATS_USE_TLS=true
MEME_NATS_TOKEN=
