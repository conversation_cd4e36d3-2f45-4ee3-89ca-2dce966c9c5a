# Production Environment Configuration
# This file contains environment variables for production deployment
# IMPORTANT: Update all sensitive values before deploying to production

# Application Configuration
APP_ENV=production
APP_NAME=xbit-agent
APP_VERSION=1.0.0

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# Database Configuration (PostgreSQL) - Legacy variables for backward compatibility
# IMPORTANT: Update these with your production database credentials
POSTGRES_HOST=your-production-db-host
POSTGRES_PORT=5432
POSTGRES_USER=your-production-db-user
POSTGRES_PASS=your-production-db-password
POSTGRES_DB=agent

# New Database Configuration for Agency (PostgreSQL) - Primary configuration for production
# IMPORTANT: Update these with your production agency database credentials
POSTGRES_AGENCY_HOST=your-production-agency-db-host
POSTGRES_AGENCY_PORT=5432
POSTGRES_AGENCY_USER=dex
POSTGRES_AGENCY_PASS=your-production-agency-db-password
POSTGRES_AGENCY_SSL_MODE=enable

# Database URL for migrations (using agency configuration)
# Note: This should match POSTGRES_AGENCY_SSL_MODE setting
DATABASE_URL=***************************************************************************************/agent?sslmode=enable

# Redis Configuration (Optional)
REDIS_HOST=your-production-redis-host
REDIS_PORT=6379
REDIS_PASS=your-production-redis-password
REDIS_DB=0

# JWT Configuration
# IMPORTANT: Generate a strong secret key for production
JWT_SECRET=your-super-strong-production-jwt-secret-key-min-32-chars
JWT_EXPIRES_TIME=7d
JWT_BUFFER_TIME=1d
JWT_ISSUER=xbit-agent-production

# Logging Configuration
LOG_LEVEL=warn
LOG_FORMAT=json
LOG_DIRECTOR=log
LOG_SHOW_LINE=false
LOG_IN_CONSOLE=false

# CORS Configuration
# IMPORTANT: Restrict origins to your frontend domains
CORS_ALLOW_ORIGINS=https://your-frontend-domain.com,https://your-admin-domain.com
CORS_ALLOW_METHODS=GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS
CORS_ALLOW_HEADERS=Origin,Content-Length,Content-Type,Authorization,X-Requested-With
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_COUNT=10000
RATE_LIMIT_TIME=3600

# Production Settings
DEBUG=false
ENABLE_PLAYGROUND=false

# SSL/TLS Configuration (if applicable)
# SSL_CERT_PATH=/path/to/ssl/cert.pem
# SSL_KEY_PATH=/path/to/ssl/key.pem

# Monitoring and Observability (Optional)
# SENTRY_DSN=your-sentry-dsn
# DATADOG_API_KEY=your-datadog-api-key
