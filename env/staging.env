# Staging Environment Configuration
# This file contains environment variables for staging deployment

# Application Configuration
APP_ENV=staging
APP_NAME=xbit-agent
APP_VERSION=1.0.0

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# Database Configuration (PostgreSQL) - Legacy variables for backward compatibility
# Update these with your staging database credentials
POSTGRES_HOST=your-staging-db-host
POSTGRES_PORT=5432
POSTGRES_USER=your-staging-db-user
POSTGRES_PASS=your-staging-db-password
POSTGRES_DB=agent

# New Database Configuration for Agency (PostgreSQL) - Primary configuration for staging
POSTGRES_AGENCY_HOST=your-staging-agency-db-host
POSTGRES_AGENCY_PORT=5432
POSTGRES_AGENCY_USER=dex
POSTGRES_AGENCY_PASS=your-staging-agency-db-password
POSTGRES_AGENCY_SSL_MODE=require

# Database URL for migrations (using agency configuration)
DATABASE_URL=*********************************************************************************/agent?sslmode=require

# Redis Configuration (Optional)
REDIS_HOST=your-staging-redis-host
REDIS_PORT=6379
REDIS_PASS=your-staging-redis-password
REDIS_DB=0

# JWT Configuration
JWT_SECRET=staging-jwt-secret-key-change-in-production
JWT_EXPIRES_TIME=7d
JWT_BUFFER_TIME=1d
JWT_ISSUER=xbit-agent-staging

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=console
LOG_DIRECTOR=log
LOG_SHOW_LINE=true
LOG_IN_CONSOLE=true

# CORS Configuration
# Restrict origins to your staging frontend domains
CORS_ALLOW_ORIGINS=https://staging.your-domain.com
CORS_ALLOW_METHODS=GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS
CORS_ALLOW_HEADERS=Origin,Content-Length,Content-Type,Authorization,X-Requested-With
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_COUNT=15000
RATE_LIMIT_TIME=3600

# Staging Settings
DEBUG=false
ENABLE_PLAYGROUND=true
